// Test program for single stream flow control implementation
// Copyright (c) 2024 Lenovo. All rights reserved.

#include "p2psocket.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <vector>

void test_single_stream_flow_control() {
    std::cout << "Testing Single Stream Flow Control Implementation..." << std::endl;
    
    // Create socket options
    SocketOptions serverOpts = {0};
    serverOpts.mode = MODE_SERVER;
    serverOpts.type = TYPE_QUIC;
    
    SocketOptions clientOpts = {0};
    clientOpts.mode = MODE_CLIENT;
    clientOpts.type = TYPE_QUIC;
    
    // Create server socket
    P2P_SOCKET serverSocket = P2pCreate(&serverOpts);
    if (!serverSocket) {
        std::cout << "Failed to create server socket" << std::endl;
        return;
    }
    
    // Bind and listen
    if (P2pBind(serverSocket, "127.0.0.1", 12345) != 0) {
        std::cout << "Failed to bind server socket" << std::endl;
        P2pRelease(serverSocket);
        return;
    }
    
    if (P2pListen(serverSocket) != 0) {
        std::cout << "Failed to listen on server socket" << std::endl;
        P2pRelease(serverSocket);
        return;
    }
    
    std::cout << "Server listening on 127.0.0.1:12345" << std::endl;
    
    // Start server thread
    std::thread serverThread([serverSocket]() {
        // Accept connection
        P2P_SOCKET clientConn = P2pAccept(serverSocket, 30000); // 30 second timeout
        if (!clientConn) {
            std::cout << "Server: Failed to accept connection" << std::endl;
            return;
        }
        
        std::cout << "Server: Connection accepted" << std::endl;
        
        // Set to non-direct send mode to test flow control
        if (P2pSetSendMode(clientConn, 0) != 0) {
            std::cout << "Server: Failed to set send mode" << std::endl;
        } else {
            std::cout << "Server: Set to non-direct send mode for flow control testing" << std::endl;
        }
        
        std::cout << "Server: Testing flow control with large data send..." << std::endl;
        
        // Send large amount of data to test flow control
        const int bufferSize = 1024;
        const int numBuffers = 1000; // Send 1MB total
        std::vector<char> buffer(bufferSize, 'A');
        
        auto startTime = std::chrono::high_resolution_clock::now();
        
        for (int i = 0; i < numBuffers; i++) {
            // Fill buffer with pattern
            for (int j = 0; j < bufferSize; j++) {
                buffer[j] = 'A' + (i % 26);
            }
            
            int sent = P2pWrite(clientConn, buffer.data(), bufferSize);
            if (sent != bufferSize) {
                std::cout << "Server: Failed to send buffer " << i << ", sent: " << sent << std::endl;
                break;
            }
            
            if (i % 100 == 0) {
                std::cout << "Server: Sent " << i << " buffers (" << (i * bufferSize) << " bytes)" << std::endl;
            }
            
            // Small delay to allow flow control to work
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        std::cout << "Server: Finished sending " << (numBuffers * bufferSize) << " bytes in " 
                  << duration.count() << " ms" << std::endl;
        
        // Close connection
        P2pClose(clientConn);
    });
    
    // Give server time to start
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Create client socket
    P2P_SOCKET clientSocket = P2pCreate(&clientOpts);
    if (!clientSocket) {
        std::cout << "Failed to create client socket" << std::endl;
        serverThread.join();
        P2pRelease(serverSocket);
        return;
    }
    
    // Connect to server
    if (P2pConnect(clientSocket, "127.0.0.1", 12345) != 0) {
        std::cout << "Failed to connect to server" << std::endl;
        P2pRelease(clientSocket);
        serverThread.join();
        P2pRelease(serverSocket);
        return;
    }
    
    std::cout << "Client: Connected to server" << std::endl;
    
    // Receive data
    const int bufferSize = 1024;
    std::vector<char> recvBuffer(bufferSize);
    int totalReceived = 0;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    while (true) {
        int received = P2pRead(clientSocket, recvBuffer.data(), bufferSize);
        if (received <= 0) {
            break;
        }
        
        totalReceived += received;
        
        if (totalReceived % (100 * bufferSize) == 0) {
            std::cout << "Client: Received " << totalReceived << " bytes" << std::endl;
        }
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    std::cout << "Client: Total received: " << totalReceived << " bytes in " 
              << duration.count() << " ms" << std::endl;
    
    // Clean up
    P2pClose(clientSocket);
    serverThread.join();
    P2pClose(serverSocket);
    
    std::cout << "Single stream flow control test completed successfully!" << std::endl;
}

int main() {
    test_single_stream_flow_control();
    return 0;
}
