# P2P Socket Stream API

This document describes the new Stream API added to the p2psocket library, which provides support for multiple QUIC streams within a single connection.

## Overview

The Stream API allows applications to create and manage multiple independent streams over a single QUIC connection. This enables:

- **Multiplexing**: Multiple data flows over one connection
- **Prioritization**: Different streams can have different priorities
- **Isolation**: Each stream has independent flow control and buffering
- **Flexibility**: Support for both unidirectional and bidirectional streams

## Backward Compatibility

The existing p2psocket API (P2pWrite, P2pRead, etc.) continues to work unchanged. These functions operate on a default stream, ensuring existing code requires no modifications.

## Data Types

### Stream Handle
```c
typedef void* P2P_STREAM;
```

### Stream Options
```c
struct StreamOptions {
    int unidirectional;  // 0 = bidirectional, 1 = unidirectional
    int priority;        // 0-255, higher value = higher priority
    int buffer_size;     // Buffer size for this stream
};
```

### Stream Events
```c
enum STREAM_EVENT {
    STREAM_EVENT_DATA_RECEIVED = 1,
    STREAM_EVENT_SEND_COMPLETE = 2,
    STREAM_EVENT_PEER_CLOSED = 3,
    STREAM_EVENT_ERROR = 4,
};

struct StreamEvent {
    enum STREAM_EVENT type;
    P2P_STREAM stream;
    union {
        struct {
            const char* data;
            int length;
        } data_received;
        struct {
            int bytes_sent;
            int error_code;
        } send_complete;
        struct {
            int error_code;
        } error;
    };
};
```

### Stream Callback
```c
typedef void (*StreamEventCallback)(P2P_STREAM stream, struct StreamEvent* event, void* user_data);
```

## API Functions

### Stream Management

#### P2pStreamCreate
```c
P2P_STREAM P2pStreamCreate(P2P_SOCKET soc, struct StreamOptions* options);
```
Creates a new stream on the specified socket.
- **soc**: Connected P2P socket
- **options**: Stream configuration (NULL for defaults)
- **Returns**: Stream handle or NULL on failure

#### P2pStreamClose
```c
int P2pStreamClose(P2P_STREAM stream);
```
Closes a stream and releases its resources.
- **stream**: Stream handle
- **Returns**: 0 on success, -1 on failure

### Data Transmission

#### P2pStreamWrite
```c
int P2pStreamWrite(P2P_STREAM stream, const char* buffer, int len);
```
Sends data on a stream.
- **stream**: Stream handle
- **buffer**: Data to send
- **len**: Number of bytes to send
- **Returns**: Number of bytes sent, or -1 on failure

#### P2pStreamWritev
```c
int P2pStreamWritev(P2P_STREAM stream, struct p2p_iovec* iov, int count);
```
Sends vectored data on a stream.
- **stream**: Stream handle
- **iov**: Array of I/O vectors
- **count**: Number of vectors
- **Returns**: Total bytes sent, or -1 on failure

#### P2pStreamRead
```c
int P2pStreamRead(P2P_STREAM stream, char* buffer, int len);
```
Reads data from a stream.
- **stream**: Stream handle
- **buffer**: Buffer to store received data
- **len**: Buffer size
- **Returns**: Number of bytes read, or -1 on failure

### Event Handling

#### P2pStreamPoll
```c
int P2pStreamPoll(P2P_STREAM stream, struct StreamEvent* events, int max_events, int timeout);
```
Polls for stream events.
- **stream**: Stream handle
- **events**: Array to store events
- **max_events**: Maximum number of events to return
- **timeout**: Timeout in milliseconds (0 = non-blocking)
- **Returns**: Number of events returned, or -1 on failure

#### P2pStreamSetCallback
```c
int P2pStreamSetCallback(P2P_STREAM stream, StreamEventCallback callback, void* user_data);
```
Sets an event callback for a stream.
- **stream**: Stream handle
- **callback**: Callback function
- **user_data**: User data passed to callback
- **Returns**: 0 on success, -1 on failure

### Information Query

#### P2pStreamGetId
```c
int P2pStreamGetId(P2P_STREAM stream);
```
Gets the stream ID.

#### P2pStreamGetState
```c
int P2pStreamGetState(P2P_STREAM stream);
```
Gets the current stream state.

#### P2pStreamGetBufferedBytes
```c
int P2pStreamGetBufferedBytes(P2P_STREAM stream);
```
Gets the number of buffered bytes.

#### P2pStreamGetSocket
```c
P2P_SOCKET P2pStreamGetSocket(P2P_STREAM stream);
```
Gets the parent socket handle.

## Usage Examples

### Basic Stream Creation
```c
// Create socket and connect
P2P_SOCKET socket = P2pCreate(&options);
P2pConnect(socket, "127.0.0.1", 4433);

// Create a bidirectional stream
struct StreamOptions streamOpts = {0};
streamOpts.unidirectional = 0;
streamOpts.priority = 128;
streamOpts.buffer_size = 64 * 1024;

P2P_STREAM stream = P2pStreamCreate(socket, &streamOpts);
```

### Event-Driven Programming
```c
void on_stream_event(P2P_STREAM stream, struct StreamEvent* event, void* user_data) {
    switch (event->type) {
        case STREAM_EVENT_DATA_RECEIVED:
            // Handle received data
            char buffer[1024];
            int bytes = P2pStreamRead(stream, buffer, sizeof(buffer));
            break;
        case STREAM_EVENT_SEND_COMPLETE:
            // Handle send completion
            break;
    }
}

// Set callback
P2pStreamSetCallback(stream, on_stream_event, NULL);
```

### Polling Mode
```c
struct StreamEvent events[10];
int count = P2pStreamPoll(stream, events, 10, 1000); // 1 second timeout

for (int i = 0; i < count; i++) {
    // Process events
}
```

### Multiple Streams
```c
// Control stream (high priority)
struct StreamOptions controlOpts = {0, 255, 32*1024};
P2P_STREAM controlStream = P2pStreamCreate(socket, &controlOpts);

// Data stream (medium priority)
struct StreamOptions dataOpts = {0, 128, 128*1024};
P2P_STREAM dataStream = P2pStreamCreate(socket, &dataOpts);

// Log stream (low priority, unidirectional)
struct StreamOptions logOpts = {1, 64, 64*1024};
P2P_STREAM logStream = P2pStreamCreate(socket, &logOpts);
```

## Best Practices

1. **Stream Lifecycle**: Always close streams when done to free resources
2. **Error Handling**: Check return values and handle errors appropriately
3. **Prioritization**: Use priorities to ensure important streams get bandwidth
4. **Buffer Sizes**: Choose appropriate buffer sizes based on expected data volume
5. **Event Handling**: Use callbacks for responsive applications, polling for simple cases

## Limitations

- Maximum number of streams per socket: 100 (configurable)
- Stream creation requires an established connection
- Unidirectional streams can only send data (client to server)

## Files

- `test_stream_api.c`: Unit tests for Stream API
- `stream_api_example.c`: Complete usage example
- `p2psocket.h`: API declarations
- `p2pquic.cpp`: Implementation
