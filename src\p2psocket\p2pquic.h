// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#pragma once
#include "common_defines.h"

P2P_SOCKET QuicCreate(SocketOptions* option);
int QuicPoll(P2P_SOCKET soc, PollEvent* events, int timeout);
int QuicSetConnTimeout(P2P_SOCKET soc, int timeout);
int QuicBind(P2P_SOCKET soc, const char* ipaddr, int port);
int QuicConnect(P2P_SOCKET soc, const char* ipaddr, int port);
int QuicWrite(P2P_SOCKET soc, const char* buffer, int len);
int QuicWritev(P2P_SOCKET soc, struct p2p_iovec* iov, int count);
int QuicRead(P2P_SOCKET soc, char* buffer, int len);
int QuicSetReadMode(P2P_SOCKET soc, int directMode);
int QuicSetSendMode(P2P_SOCKET soc, int sendBufferMode);
P2P_SOCKET QuicAccept(P2P_SOCKET soc,
                                char* ipaddr,
                                int ipaddr_len,
                                int* port);
int QuicListen(P2P_SOCKET soc);
int QuicGetLocalPort(P2P_SOCKET soc);
int QuicClose(P2P_SOCKET soc);
void QuicRelease(P2P_SOCKET soc);

// Stream API functions for QUIC
P2P_STREAM QuicStreamCreate(P2P_SOCKET soc, struct StreamOptions* options);
int QuicStreamClose(P2P_STREAM stream);
int QuicStreamWrite(P2P_STREAM stream, const char* buffer, int len);
int QuicStreamWritev(P2P_STREAM stream, struct p2p_iovec* iov, int count);
int QuicStreamRead(P2P_STREAM stream, char* buffer, int len);
int QuicStreamPoll(P2P_STREAM stream, struct StreamEvent* events, int max_events, int timeout);
int QuicStreamGetState(P2P_STREAM stream);
int QuicStreamSetCallback(P2P_STREAM stream, StreamEventCallback callback, void* user_data);
int QuicStreamGetId(P2P_STREAM stream);
int QuicStreamGetBufferedBytes(P2P_STREAM stream);
P2P_SOCKET QuicStreamGetSocket(P2P_STREAM stream);


