# Multi-Stream Test Programs

This directory contains several test programs for the P2P Socket Stream API functionality.

## Test Programs

### 1. test_multistream
A comprehensive multi-stream test program that supports various testing scenarios.

**Features:**
- Multiple test scenarios (single stream, multi-stream, priority testing, etc.)
- Both server and client modes
- Configurable stream count, data size, and priorities
- Support for both callback and poll event handling modes
- Real-time statistics and performance monitoring
- Thread-safe concurrent stream operations

**Usage:**
```bash
# Server mode (listen for connections)
./test_multistream -ip 0 -scenario 2 -streams 4 -callback

# Client mode (connect to server)
./test_multistream -ip ************* -scenario 2 -streams 4 -size 50

# Show help
./test_multistream -h
```

**Command Line Options:**
- `-ip <address>`: Server IP address (0 for server mode)
- `-scenario <1-6>`: Test scenario (1=single stream, 2=multi-stream, etc.)
- `-streams <1-10>`: Number of streams to create
- `-size <MB>`: Data size per stream in MB
- `-priority <1-5>`: Number of priority levels
- `-unidirectional <0-100>`: Percentage of unidirectional streams
- `-buffer <KB>`: Buffer size in KB
- `-duration <seconds>`: Test duration in seconds
- `-callback`: Use callback mode for events
- `-poll`: Use poll mode for events

### 2. test_stream_api
A unit test program that validates basic Stream API functionality.

**Features:**
- Stream creation and destruction tests
- Data transmission tests
- Event handling verification
- Multiple stream management tests
- API error handling tests

**Usage:**
```bash
./test_stream_api
```

### 3. stream_api_example
A simple example program demonstrating how to use the Stream API.

**Features:**
- Basic stream creation example
- Event-driven programming example
- Multiple stream types (control, data, logging)
- Both callback and poll mode examples

**Usage:**
```bash
# Server mode
./stream_api_example

# Client mode
./stream_api_example ************* 4433
```

## Test Scenarios

### Scenario 1: Single Stream Baseline
- Creates only one stream
- Provides baseline performance comparison
- Uses poll mode by default

### Scenario 2: Multi-Stream Concurrent
- Creates multiple streams simultaneously
- Tests concurrent data transmission
- Validates stream isolation

### Scenario 3: Priority Test
- Creates streams with different priorities
- Verifies priority-based transmission order
- Tests QoS functionality

### Scenario 4: Callback Mode Test
- All streams use callback-based event handling
- Tests asynchronous event processing
- Validates callback thread safety

### Scenario 5: Poll Mode Test
- All streams use poll-based event handling
- Tests synchronous event processing
- Validates poll timeout behavior

### Scenario 6: Mixed Mode Test
- Half streams use callbacks, half use polling
- Tests mixed event handling modes
- Validates mode coexistence

## Building with CMake

The test programs are automatically built when you build the p2psocket library:

```bash
# Configure
cmake -B build -S .

# Build
cmake --build build --config Release

# The executables will be in build/bin/
```

## Example Test Runs

### Basic Multi-Stream Test
```bash
# Terminal 1 (Server)
./test_multistream -ip 0 -scenario 2 -streams 4 -duration 30

# Terminal 2 (Client)
./test_multistream -ip 127.0.0.1 -scenario 2 -streams 4 -size 100
```

### Priority Testing
```bash
# Terminal 1 (Server)
./test_multistream -ip 0 -scenario 3 -streams 6 -priority 3

# Terminal 2 (Client)
./test_multistream -ip 127.0.0.1 -scenario 3 -streams 6 -priority 3
```

### Callback vs Poll Comparison
```bash
# Test with callbacks
./test_multistream -ip 127.0.0.1 -scenario 4 -streams 4 -callback

# Test with polling
./test_multistream -ip 127.0.0.1 -scenario 5 -streams 4 -poll
```

## Performance Metrics

The test programs provide detailed performance metrics:

- **Throughput**: Mbps per stream and total
- **Packet Count**: Packets sent/received per stream
- **Error Rate**: Number of transmission errors
- **Latency**: Average latency per stream (when available)
- **Resource Usage**: Memory and CPU utilization

## Troubleshooting

### Common Issues

1. **Connection Failed**: Ensure server is running and firewall allows connections
2. **Stream Creation Failed**: Check if connection is established before creating streams
3. **Performance Issues**: Try adjusting buffer sizes and stream counts
4. **Memory Issues**: Reduce stream count or data size for resource-constrained systems

### Debug Options

- Set log level to verbose for detailed debugging
- Use smaller data sizes for initial testing
- Start with single stream scenario before testing multiple streams

## Integration with Existing Tests

These test programs complement the existing `testcapability.cpp`:

- `testcapability.cpp`: Tests single-stream performance and compatibility
- `test_multistream.cpp`: Tests multi-stream functionality and performance
- `test_stream_api.c`: Tests Stream API correctness and edge cases
- `stream_api_example.c`: Provides usage examples and tutorials

## Future Enhancements

Planned improvements for the test programs:

- Bandwidth limiting and QoS testing
- Network simulation (packet loss, latency)
- Stress testing with hundreds of streams
- Performance profiling and optimization
- Cross-platform compatibility testing
