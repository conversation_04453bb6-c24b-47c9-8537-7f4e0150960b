// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
// Example usage of the Stream API

#include "p2psocket.h"
#include "common_defines.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// Global variables for the example
P2P_SOCKET g_socket = NULL;
P2P_STREAM g_streams[3] = {NULL, NULL, NULL};

// Stream event callback function
void on_stream_event(P2P_STREAM stream, struct StreamEvent* event, void* user_data) {
    int* stream_id = (int*)user_data;
    
    printf("[Stream %d] Event: ", stream_id ? *stream_id : -1);
    
    switch (event->type) {
        case STREAM_EVENT_DATA_RECEIVED:
            printf("Data received (%d bytes)\n", event->data_received.length);
            
            // Read the data
            char buffer[1024];
            int bytesRead = P2pStreamRead(stream, buffer, sizeof(buffer) - 1);
            if (bytesRead > 0) {
                buffer[bytesRead] = '\0';
                printf("[Stream %d] Received: %s\n", stream_id ? *stream_id : -1, buffer);
            }
            break;
            
        case STREAM_EVENT_SEND_COMPLETE:
            printf("Send complete (%d bytes)\n", event->send_complete.bytes_sent);
            break;
            
        case STREAM_EVENT_PEER_CLOSED:
            printf("Peer closed the stream\n");
            break;
            
        case STREAM_EVENT_ERROR:
            printf("Stream error (code: %d)\n", event->error.error_code);
            break;
    }
}

// Initialize the socket and connection
int initialize_connection(const char* server_ip, int server_port) {
    printf("Initializing connection to %s:%d...\n", server_ip, server_port);
    
    // Create socket options
    struct SocketOptions options = {0};
    options.mode = MODE_CLIENT;
    options.type = SOCKET_QUIC;
    options.log_level = P2P_LOG_INFO;
    
    // Create socket
    g_socket = P2pCreate(&options);
    if (g_socket == NULL) {
        printf("Failed to create socket\n");
        return -1;
    }
    
    // Connect to server
    int result = P2pConnect(g_socket, server_ip, server_port);
    if (result != 0) {
        printf("Failed to connect to server\n");
        P2pClose(g_socket);
        g_socket = NULL;
        return -1;
    }
    
    printf("Connected successfully!\n");
    return 0;
}

// Create multiple streams with different configurations
int create_streams() {
    printf("Creating streams...\n");
    
    if (g_socket == NULL) {
        printf("Socket not initialized\n");
        return -1;
    }
    
    // Stream 1: Bidirectional, high priority, for control messages
    struct StreamOptions stream1_opts = {0};
    stream1_opts.unidirectional = 0;
    stream1_opts.priority = 255; // High priority
    stream1_opts.buffer_size = 32 * 1024;
    
    g_streams[0] = P2pStreamCreate(g_socket, &stream1_opts);
    if (g_streams[0] != NULL) {
        static int stream1_id = 1;
        P2pStreamSetCallback(g_streams[0], on_stream_event, &stream1_id);
        printf("  Stream 1 (control): Created successfully\n");
    }
    
    // Stream 2: Bidirectional, medium priority, for data transfer
    struct StreamOptions stream2_opts = {0};
    stream2_opts.unidirectional = 0;
    stream2_opts.priority = 128; // Medium priority
    stream2_opts.buffer_size = 128 * 1024;
    
    g_streams[1] = P2pStreamCreate(g_socket, &stream2_opts);
    if (g_streams[1] != NULL) {
        static int stream2_id = 2;
        P2pStreamSetCallback(g_streams[1], on_stream_event, &stream2_id);
        printf("  Stream 2 (data): Created successfully\n");
    }
    
    // Stream 3: Unidirectional, low priority, for logging
    struct StreamOptions stream3_opts = {0};
    stream3_opts.unidirectional = 1;
    stream3_opts.priority = 64; // Low priority
    stream3_opts.buffer_size = 64 * 1024;
    
    g_streams[2] = P2pStreamCreate(g_socket, &stream3_opts);
    if (g_streams[2] != NULL) {
        static int stream3_id = 3;
        P2pStreamSetCallback(g_streams[2], on_stream_event, &stream3_id);
        printf("  Stream 3 (logging): Created successfully\n");
    }
    
    return 0;
}

// Send data on different streams
int send_test_data() {
    printf("Sending test data...\n");
    
    // Send control message on stream 1
    if (g_streams[0] != NULL) {
        const char* control_msg = "CONTROL: Hello from client";
        int result = P2pStreamWrite(g_streams[0], control_msg, strlen(control_msg));
        printf("  Control message sent: %d bytes\n", result);
    }
    
    // Send data on stream 2 using vectored I/O
    if (g_streams[1] != NULL) {
        struct p2p_iovec iov[3];
        const char* header = "DATA_HEADER:";
        const char* payload = "This is the main data payload";
        const char* footer = ":END";
        
        iov[0].iov_base = (void*)header;
        iov[0].iov_len = strlen(header);
        iov[1].iov_base = (void*)payload;
        iov[1].iov_len = strlen(payload);
        iov[2].iov_base = (void*)footer;
        iov[2].iov_len = strlen(footer);
        
        int result = P2pStreamWritev(g_streams[1], iov, 3);
        printf("  Data message sent: %d bytes\n", result);
    }
    
    // Send log message on stream 3
    if (g_streams[2] != NULL) {
        const char* log_msg = "LOG: Client operation completed";
        int result = P2pStreamWrite(g_streams[2], log_msg, strlen(log_msg));
        printf("  Log message sent: %d bytes\n", result);
    }
    
    return 0;
}

// Poll for events on all streams
int poll_stream_events(int timeout_ms) {
    printf("Polling for stream events (timeout: %d ms)...\n", timeout_ms);
    
    int total_events = 0;
    
    for (int i = 0; i < 3; i++) {
        if (g_streams[i] != NULL) {
            struct StreamEvent events[10];
            int event_count = P2pStreamPoll(g_streams[i], events, 10, timeout_ms);
            
            if (event_count > 0) {
                printf("  Stream %d: %d events\n", i + 1, event_count);
                total_events += event_count;
                
                // Process events (callbacks will also be called)
                for (int j = 0; j < event_count; j++) {
                    // Events are already processed by callbacks
                    // This is just for demonstration
                }
            }
        }
    }
    
    printf("  Total events: %d\n", total_events);
    return total_events;
}

// Clean up resources
void cleanup() {
    printf("Cleaning up...\n");
    
    // Close all streams
    for (int i = 0; i < 3; i++) {
        if (g_streams[i] != NULL) {
            P2pStreamClose(g_streams[i]);
            g_streams[i] = NULL;
        }
    }
    
    // Close socket
    if (g_socket != NULL) {
        P2pClose(g_socket);
        g_socket = NULL;
    }
    
    printf("Cleanup completed\n");
}

// Main example function
int main(int argc, char* argv[]) {
    printf("Stream API Example\n");
    printf("==================\n\n");
    
    // Default server settings
    const char* server_ip = "127.0.0.1";
    int server_port = 4433;
    
    // Parse command line arguments
    if (argc >= 3) {
        server_ip = argv[1];
        server_port = atoi(argv[2]);
    }
    
    int result = 0;
    
    // Initialize connection
    if (initialize_connection(server_ip, server_port) != 0) {
        printf("Failed to initialize connection\n");
        return -1;
    }
    
    // Create streams
    if (create_streams() != 0) {
        printf("Failed to create streams\n");
        cleanup();
        return -1;
    }
    
    // Send test data
    if (send_test_data() != 0) {
        printf("Failed to send test data\n");
        cleanup();
        return -1;
    }
    
    // Poll for events
    poll_stream_events(5000); // 5 second timeout
    
    // Clean up
    cleanup();
    
    printf("\nExample completed successfully!\n");
    return 0;
}
