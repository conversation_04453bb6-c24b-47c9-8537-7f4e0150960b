// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#pragma once
#include "common_defines.h"
#ifdef FS_STATIC_LIB
#define PLUGIN_API
#else
#if defined(WIN32) || defined(_WIN32)
#ifdef DLL_EXPORT
#define PLUGIN_API __declspec(dllexport)
#else
#define PLUGIN_API __declspec(dllimport)
#endif
#endif

#if defined(ANDROID) || defined(__linux__)
#define PLUGIN_API __attribute__((visibility("default")))
#endif
#endif

#ifdef __cplusplus
extern "C" {
#endif
PLUGIN_API P2P_SOCKET P2pCreate(SocketOptions* option);
PLUGIN_API int P2pPoll(P2P_SOCKET soc, PollEvent* events, int timeout);
PLUGIN_API int P2pSetConnTimeout(P2P_SOCKET soc, int timeout);
PLUGIN_API int P2pBind(P2P_SOCKET soc, const char* ipaddr, int port);
PLUGIN_API int P2pConnect(P2P_SOCKET soc, const char* ipaddr, int port);
PLUGIN_API int P2pWrite(P2P_SOCKET soc, const char* buffer, int len);
PLUGIN_API int P2pWritev(P2P_SOCKET soc, struct p2p_iovec* iov, int count);
PLUGIN_API int P2pRead(P2P_SOCKET soc, char* buffer, int len);
PLUGIN_API int P2pSetSendMode(P2P_SOCKET soc, int directMode);
PLUGIN_API int P2pSetReadMode(P2P_SOCKET soc, int directMode);
PLUGIN_API P2P_SOCKET P2pAccept(P2P_SOCKET soc,
                                char* ipaddr,
                                int ipaddr_len,
                                int* port);
PLUGIN_API int P2pListen(P2P_SOCKET soc);
PLUGIN_API int P2pGetLocalPort(P2P_SOCKET soc);
PLUGIN_API int P2pClose(P2P_SOCKET soc);

// Stream management API
PLUGIN_API P2P_STREAM P2pStreamCreate(P2P_SOCKET soc, struct StreamOptions* options);
PLUGIN_API int P2pStreamClose(P2P_STREAM stream);

// Stream data transmission API
PLUGIN_API int P2pStreamWrite(P2P_STREAM stream, const char* buffer, int len);
PLUGIN_API int P2pStreamWritev(P2P_STREAM stream, struct p2p_iovec* iov, int count);
PLUGIN_API int P2pStreamRead(P2P_STREAM stream, char* buffer, int len);

// Stream state and control API
PLUGIN_API int P2pStreamPoll(P2P_STREAM stream, struct StreamEvent* events, int max_events, int timeout);
PLUGIN_API int P2pStreamGetState(P2P_STREAM stream);
PLUGIN_API int P2pStreamSetCallback(P2P_STREAM stream, StreamEventCallback callback, void* user_data);

// Stream information query API
PLUGIN_API int P2pStreamGetId(P2P_STREAM stream);
PLUGIN_API int P2pStreamGetBufferedBytes(P2P_STREAM stream);
PLUGIN_API P2P_SOCKET P2pStreamGetSocket(P2P_STREAM stream);

#ifdef __cplusplus
}
#endif
