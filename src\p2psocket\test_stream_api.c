// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
// Test file for Stream API functionality

#include "p2psocket.h"
#include "common_defines.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// Test callback function for stream events
void stream_event_callback(P2P_STREAM stream, struct StreamEvent* event, void* user_data) {
    printf("Stream event callback: type=%d, stream=%p\n", event->type, stream);
    
    switch (event->type) {
        case STREAM_EVENT_DATA_RECEIVED:
            printf("  Data received: %d bytes\n", event->data_received.length);
            break;
        case STREAM_EVENT_SEND_COMPLETE:
            printf("  Send complete: %d bytes sent\n", event->send_complete.bytes_sent);
            break;
        case STREAM_EVENT_PEER_CLOSED:
            printf("  Peer closed stream\n");
            break;
        case STREAM_EVENT_ERROR:
            printf("  Stream error: %d\n", event->error.error_code);
            break;
    }
}

// Test basic stream creation and management
int test_stream_creation() {
    printf("Testing stream creation...\n");
    
    // Create socket options
    struct SocketOptions options = {0};
    options.mode = MODE_CLIENT;
    options.type = SOCKET_QUIC;
    options.log_level = P2P_LOG_INFO;
    
    // Create socket
    P2P_SOCKET socket = P2pCreate(&options);
    if (socket == NULL) {
        printf("Failed to create socket\n");
        return -1;
    }
    
    // For this test, we'll simulate a connected state
    // In real usage, you would call P2pConnect first
    
    // Create stream options
    struct StreamOptions streamOpts = {0};
    streamOpts.unidirectional = 0;
    streamOpts.priority = 0;
    streamOpts.buffer_size = 64 * 1024;
    
    // Create stream (this will fail without a connection, but tests the API)
    P2P_STREAM stream = P2pStreamCreate(socket, &streamOpts);
    if (stream == NULL) {
        printf("Stream creation failed (expected without connection)\n");
    } else {
        printf("Stream created successfully: %p\n", stream);
        
        // Test stream information functions
        int streamId = P2pStreamGetId(stream);
        int state = P2pStreamGetState(stream);
        int bufferedBytes = P2pStreamGetBufferedBytes(stream);
        P2P_SOCKET parentSocket = P2pStreamGetSocket(stream);
        
        printf("  Stream ID: %d\n", streamId);
        printf("  Stream state: %d\n", state);
        printf("  Buffered bytes: %d\n", bufferedBytes);
        printf("  Parent socket: %p\n", parentSocket);
        
        // Set callback
        int result = P2pStreamSetCallback(stream, stream_event_callback, NULL);
        printf("  Set callback result: %d\n", result);
        
        // Close stream
        result = P2pStreamClose(stream);
        printf("  Close stream result: %d\n", result);
    }
    
    // Close socket
    P2pClose(socket);
    
    printf("Stream creation test completed\n");
    return 0;
}

// Test stream data operations
int test_stream_data_operations() {
    printf("Testing stream data operations...\n");
    
    // Create socket options
    struct SocketOptions options = {0};
    options.mode = MODE_CLIENT;
    options.type = SOCKET_QUIC;
    options.log_level = P2P_LOG_INFO;
    
    // Create socket
    P2P_SOCKET socket = P2pCreate(&options);
    if (socket == NULL) {
        printf("Failed to create socket\n");
        return -1;
    }
    
    // Create stream (will fail without connection, but tests the API)
    P2P_STREAM stream = P2pStreamCreate(socket, NULL);
    if (stream != NULL) {
        // Test write operations
        const char* testData = "Hello, Stream API!";
        int writeResult = P2pStreamWrite(stream, testData, strlen(testData));
        printf("  Write result: %d\n", writeResult);
        
        // Test vectored write
        struct p2p_iovec iov[2];
        const char* data1 = "Part 1 ";
        const char* data2 = "Part 2";
        iov[0].iov_base = (void*)data1;
        iov[0].iov_len = strlen(data1);
        iov[1].iov_base = (void*)data2;
        iov[1].iov_len = strlen(data2);
        
        int writevResult = P2pStreamWritev(stream, iov, 2);
        printf("  Writev result: %d\n", writevResult);
        
        // Test read operations
        char readBuffer[256];
        int readResult = P2pStreamRead(stream, readBuffer, sizeof(readBuffer));
        printf("  Read result: %d\n", readResult);
        
        // Test poll operations
        struct StreamEvent events[10];
        int pollResult = P2pStreamPoll(stream, events, 10, 0); // Non-blocking poll
        printf("  Poll result: %d events\n", pollResult);
        
        // Close stream
        P2pStreamClose(stream);
    }
    
    // Close socket
    P2pClose(socket);
    
    printf("Stream data operations test completed\n");
    return 0;
}

// Test multiple streams
int test_multiple_streams() {
    printf("Testing multiple streams...\n");
    
    // Create socket options
    struct SocketOptions options = {0};
    options.mode = MODE_CLIENT;
    options.type = SOCKET_QUIC;
    options.log_level = P2P_LOG_INFO;
    
    // Create socket
    P2P_SOCKET socket = P2pCreate(&options);
    if (socket == NULL) {
        printf("Failed to create socket\n");
        return -1;
    }
    
    // Try to create multiple streams
    P2P_STREAM streams[5];
    int createdStreams = 0;
    
    for (int i = 0; i < 5; i++) {
        struct StreamOptions streamOpts = {0};
        streamOpts.unidirectional = (i % 2); // Alternate between bidirectional and unidirectional
        streamOpts.priority = i;
        streamOpts.buffer_size = (32 + i * 16) * 1024;
        
        streams[i] = P2pStreamCreate(socket, &streamOpts);
        if (streams[i] != NULL) {
            createdStreams++;
            printf("  Created stream %d: %p\n", i, streams[i]);
        }
    }
    
    printf("  Total streams created: %d\n", createdStreams);
    
    // Close all created streams
    for (int i = 0; i < 5; i++) {
        if (streams[i] != NULL) {
            P2pStreamClose(streams[i]);
        }
    }
    
    // Close socket
    P2pClose(socket);
    
    printf("Multiple streams test completed\n");
    return 0;
}

// Main test function
int main() {
    printf("Starting Stream API tests...\n\n");
    
    int result = 0;
    
    // Run tests
    result |= test_stream_creation();
    printf("\n");
    
    result |= test_stream_data_operations();
    printf("\n");
    
    result |= test_multiple_streams();
    printf("\n");
    
    if (result == 0) {
        printf("All Stream API tests completed successfully!\n");
    } else {
        printf("Some Stream API tests failed.\n");
    }
    
    return result;
}
