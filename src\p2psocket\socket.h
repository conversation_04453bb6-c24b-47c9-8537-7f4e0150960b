// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#pragma once
#include <functional>

//#include "cfg.h"
#include "common_defines.h"
#include <string>
const static int BLOCK_SIZE = 2048 * 1024;  // 2MB
class SocketInterface {
 public:
  virtual ~SocketInterface() = default;
  virtual int P2pBind(const char* ipaddr, int port) = 0;
  virtual int P2pBind2(const char* ipaddr, int port[], int size);
  virtual int P2pConnect(const char* ipaddr, int port) = 0;
  virtual int P2pWrite(const char* buffer, int len) = 0;
  virtual int P2pRead(char* buffer, int len) = 0;
  virtual int P2pRead(char* buffer, int len, int timeout);
  virtual SocketInterface* P2pAccept(char* ipaddr,
                                     int ipaddr_len,
                                     int* port) = 0;
  virtual SocketInterface* P2pAccept();
  virtual int P2pListen() = 0;
  virtual int P2pClose() = 0;
  virtual int P2pWritev(struct p2p_iovec* iov, int count);

  virtual int P2pReadv(struct p2p_iovec* iov, int count);
  virtual int P2pReadv(struct p2p_iovec* iov, int count, int timeout);
  virtual int P2pSetNonBlocking();
  virtual int P2pSetConnTimeout(int timeout);
  virtual int P2pShutdown();
  virtual int P2pRegisterStateChanged(std::function<void(int, int)> callback);
  virtual int P2pGetLocalPort() = 0;
  virtual int P2pGetLocalPorts(int* ports, int* size);
  virtual int P2pSetRecvTimeout(int timeout);
  virtual int P2pSetSendTimeout(int timeout);
  virtual int P2pSetSendMode(int directMode);
  virtual int P2pSetReadMode(int directMode);
  virtual int P2pPoll(PollEvent* events, int timeout);
  virtual int P2pSetCertVerifyCallback(
      std::function<bool(std::string& str)> callback);
  virtual void SetSocketIdx(int idx);
  virtual int GetSocketIdx();
  virtual void SetRemoteIp(char* remote_ip);
  virtual const char* GetRemoteIp();
  virtual void SetRemotePort(int remote_port);
  virtual int GetRemotePort();

  // Stream API - virtual functions for multi-stream support
  virtual P2P_STREAM P2pStreamCreate(struct StreamOptions* options);
  virtual int P2pStreamClose(P2P_STREAM stream);
  virtual int P2pStreamWrite(P2P_STREAM stream, const char* buffer, int len);
  virtual int P2pStreamWritev(P2P_STREAM stream, struct p2p_iovec* iov, int count);
  virtual int P2pStreamRead(P2P_STREAM stream, char* buffer, int len);
  virtual int P2pStreamPoll(P2P_STREAM stream, struct StreamEvent* events, int max_events, int timeout);
  virtual int P2pStreamGetState(P2P_STREAM stream);
  virtual int P2pStreamSetCallback(P2P_STREAM stream, StreamEventCallback callback, void* user_data);
  virtual int P2pStreamGetId(P2P_STREAM stream);
  virtual int P2pStreamGetBufferedBytes(P2P_STREAM stream);
  virtual P2P_SOCKET P2pStreamGetSocket(P2P_STREAM stream);

 protected:
  SocketInterface() = default;
  std::string remote_ip_;
  int remote_port_ = 0;
  int socket_idx_ = 0;
};
