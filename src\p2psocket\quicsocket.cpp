// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
// this class is for tcp blocking socket
#include "quicsocket.h"
#include "p2pquic.h"
#include "lenlog.h"
SocketInterface* QuicSocket::P2pCreate(SocketOptions* option,
    bool isouter) {
    P2P_SOCKET soc = QuicCreate(option);
    if (soc == nullptr)
        return nullptr;
    QuicSocket* quic_soc = new QuicSocket();
    if (quic_soc == nullptr) {
        return nullptr;
    }
    quic_soc->quic_sock_ = soc;
    return quic_soc;
}
QuicSocket::~QuicSocket() {
    QuicRelease(quic_sock_);
}
int QuicSocket::P2pPoll(PollEvent* events, int timeout) {
    if (quic_sock_ == nullptr)
        return -1;

    return QuicPoll(quic_sock_, events, timeout);
}
int QuicSocket::P2pRegisterStateChanged(std::function<void(int, int)> callback) {
    return 0;
}
int QuicSocket::P2pBind(const char* ipaddr, int port) {

    if (quic_sock_ == nullptr) {
        return -1;
    }

    return QuicBind(quic_sock_, ipaddr, port);
}

int QuicSocket::P2pConnect(const char* ipaddr, int port) {
    if (quic_sock_ == nullptr) {
        return -1;
    }

    return QuicConnect(quic_sock_, ipaddr, port);
}

int QuicSocket::P2pWrite(const char* buffer, int len) {
    if (quic_sock_ == nullptr) {
        return -1;
    }

    return QuicWrite(quic_sock_, buffer, len);
}

int QuicSocket::P2pRead(char* buffer, int len) {
    if (quic_sock_ == nullptr) {
        return -1;
    }
    return QuicRead(quic_sock_, buffer, len);
}

SocketInterface* QuicSocket::P2pAccept(char* ipaddr, int ipaddr_len, int* port) {
    if (quic_sock_ == nullptr) {
        return nullptr;
    }

    P2P_SOCKET soc = QuicAccept(quic_sock_, ipaddr, ipaddr_len, port);
    if (soc == nullptr)
        return nullptr;
    QuicSocket* s = new QuicSocket();
    if (s == nullptr) {
        return nullptr;
    }
    s->quic_sock_ = soc;
    return s;
}

int QuicSocket::P2pListen() {
    if (quic_sock_ == nullptr) {
        return -1;
    }

    return QuicListen(quic_sock_);
}

int QuicSocket::P2pClose() {

    if (quic_sock_ == nullptr) {
        return -1;
    }

    return QuicClose(quic_sock_);
}

int QuicSocket::P2pWritev(struct p2p_iovec* iov, int count) {

    if (quic_sock_ == nullptr) {
        return -1;
    }

    return QuicWritev(quic_sock_, iov, count);
}
int QuicSocket::P2pReadv(struct p2p_iovec* iov, int count) {
    return 0;
}
int QuicSocket::P2pShutdown() {

    return 0;
}

int QuicSocket::P2pGetLocalPort() {
    if (quic_sock_ == nullptr) {
        return -1;
    }

    return QuicGetLocalPort(quic_sock_);
}

int QuicSocket::P2pSetSendMode(int directMode) {
    if (quic_sock_ == nullptr) {
        return -1;
    }

    return QuicSetSendMode(quic_sock_, directMode);
}

int QuicSocket::P2pSetReadMode(int directMode) {
    if (quic_sock_ == nullptr) {
        return -1;
    }

    return QuicSetReadMode(quic_sock_, directMode);
}

int QuicSocket::P2pSetRecvTimeout(int timeout) {

    return 0;
}
int QuicSocket::P2pSetSendTimeout(int timeout) {
    return 0;
}

int QuicSocket::P2pSetNonBlocking() {

    return 0;
}

// Stream API implementations for QUIC socket
P2P_STREAM QuicSocket::P2pStreamCreate(struct StreamOptions* options) {
    if (quic_sock_ == nullptr) {
        return nullptr;
    }
    return QuicStreamCreate(quic_sock_, options);
}

int QuicSocket::P2pStreamClose(P2P_STREAM stream) {
    return QuicStreamClose(stream);
}

int QuicSocket::P2pStreamWrite(P2P_STREAM stream, const char* buffer, int len) {
    return QuicStreamWrite(stream, buffer, len);
}

int QuicSocket::P2pStreamWritev(P2P_STREAM stream, struct p2p_iovec* iov, int count) {
    return QuicStreamWritev(stream, iov, count);
}

int QuicSocket::P2pStreamRead(P2P_STREAM stream, char* buffer, int len) {
    return QuicStreamRead(stream, buffer, len);
}

int QuicSocket::P2pStreamPoll(P2P_STREAM stream, struct StreamEvent* events, int max_events, int timeout) {
    return QuicStreamPoll(stream, events, max_events, timeout);
}

int QuicSocket::P2pStreamGetState(P2P_STREAM stream) {
    return QuicStreamGetState(stream);
}

int QuicSocket::P2pStreamSetCallback(P2P_STREAM stream, StreamEventCallback callback, void* user_data) {
    return QuicStreamSetCallback(stream, callback, user_data);
}

int QuicSocket::P2pStreamGetId(P2P_STREAM stream) {
    return QuicStreamGetId(stream);
}

int QuicSocket::P2pStreamGetBufferedBytes(P2P_STREAM stream) {
    return QuicStreamGetBufferedBytes(stream);
}

P2P_SOCKET QuicSocket::P2pStreamGetSocket(P2P_STREAM stream) {
    // For QUIC socket, we need to check if the stream belongs to this socket
    P2P_SOCKET parent = QuicStreamGetSocket(stream);
    if (parent == quic_sock_) {
        return (P2P_SOCKET)this; // Return the QuicSocket instance
    }
    return nullptr;
}
