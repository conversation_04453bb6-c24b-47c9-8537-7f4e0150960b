// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#pragma once
#include <stdbool.h>
#include <stdint.h>
#ifndef _WIN32
#include <sys/uio.h>
#endif

typedef void* P2P_SOCKET;
typedef void (*P2pStateChanged)(P2P_SOCKET, int, int);

struct p2p_iovec {
  void* iov_base; /* Pointer to data. */
  size_t iov_len; /* Length of data. */
};
enum LOG_LEVEL {
  P2P_LOG_NONE,
  P2P_LOG_ERROR,
  P2P_LOG_WARN,
  P2P_LOG_INFO,
  P2P_LOG_DEBUG,
  P2P_LOG_VERBOSE,
};
enum EVENT {
  EVENTIN,
  EVENTOUT,
  EVENTERR,
};

enum P2P_STATE {
  P2P_STATE_DISCONNECTED = 0,
  P2P_STATE_CONNECTED = 1,
  P2P_STATE_DATA_RECEIVED = 2,
  P2P_STATE_DATA_SENT = 3,
  P2P_STATE_ERROR = 4,
};

enum SOCKET_MODE {
  MODE_SERVER,
  MODE_CLIENT,
  MODE_MAX,
};
enum SOCKET_TYPE {
  SOCKET_MULTITCP = 1,
  SOCKET_SSL,
  SOCKET_QUIC,
  SOCKET_MAX,
};
typedef bool (*CertVerifyCallback)(P2P_SOCKET, const char* x509);
struct SocketOptions {
  enum SOCKET_MODE mode;
  enum SOCKET_TYPE type;
  int wokernum;
  const char* cert;
  const char* privatekey;
  CertVerifyCallback cert_verify;
  enum LOG_LEVEL log_level;
  const char* log_path;
};
typedef struct SocketOptions SocketOptions;
struct PollEvent {
  short events;
  short revents;
};

typedef struct PollEvent PollEvent;

enum POLLEVENT {
  P2PPOLLIN = 1,
  P2PPOLLOUT = 2,
  P2PPOLLERR = 4,
};

// Stream related type definitions
typedef void* P2P_STREAM;

// Stream event types
enum STREAM_EVENT {
  STREAM_EVENT_DATA_RECEIVED = 1,
  STREAM_EVENT_SEND_COMPLETE = 2,
  STREAM_EVENT_PEER_CLOSED = 3,
  STREAM_EVENT_ERROR = 4,
};

// Stream states
enum STREAM_STATE {
  STREAM_STATE_IDLE = 0,
  STREAM_STATE_OPEN = 1,
  STREAM_STATE_SENDING = 2,
  STREAM_STATE_RECEIVING = 3,
  STREAM_STATE_CLOSED = 4,
};

// Stream options
struct StreamOptions {
  int unidirectional;  // Whether this is a unidirectional stream
  int priority;        // Stream priority (0-255, higher value = higher priority)
  int buffer_size;     // Buffer size for this stream
};

// Stream event structure
struct StreamEvent {
  enum STREAM_EVENT type;
  P2P_STREAM stream;
  union {
    struct {
      const char* data;
      int length;
    } data_received;
    struct {
      int bytes_sent;
      int error_code;
    } send_complete;
    struct {
      int error_code;
    } error;
  };
};

// Stream event callback function type
typedef void (*StreamEventCallback)(P2P_STREAM stream, struct StreamEvent* event, void* user_data);
