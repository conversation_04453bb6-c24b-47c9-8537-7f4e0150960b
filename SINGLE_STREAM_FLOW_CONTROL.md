# QUIC Single Stream Flow Control Implementation

## 概述

本实现基于 `src/perf/lib/PerfClient.cpp` 的参考逻辑，为 p2psocket 的单stream方案（QuicWrite/QuicWritev API）添加了 QUIC 流控机制。在非 `useDirectSendMode` 模式下，通过 `QUIC_STREAM_EVENT_IDEAL_SEND_BUFFER_SIZE` 和 `QUIC_STREAM_EVENT_SEND_COMPLETE` 事件来管理发送缓冲区，实现高效的流控制。

## 核心变量

在 `P2P_SOCKET_CONTEXT` 结构体中添加了以下流控变量：

```cpp
// Flow control variables for non-useDirectSendMode (single stream)
uint64_t bytesOutstanding;        // Bytes sent but not yet acknowledged
uint64_t idealSendBuffer;         // Ideal send buffer size from QUIC
uint64_t bytesSent;               // Total bytes sent
uint64_t bytesAcked;              // Total bytes acknowledged
```

### 变量说明

- **`bytesOutstanding`**: 已发送但尚未确认的字节数
- **`idealSendBuffer`**: 理想的发送缓冲区大小（从 QUIC_STREAM_EVENT_IDEAL_SEND_BUFFER_SIZE 事件获取）
- **`bytesSent`**: 总共发送的字节数
- **`bytesAcked`**: 已确认的字节数

## 关键函数

### 1. SendDataFromSocketBufferList()

新增的流控发送函数，专门用于单stream的缓冲区发送：

```cpp
static int SendDataFromSocketBufferList(P2P_SOCKET_CONTEXT* context)
```

**功能**：
- 检查流控状态：只有当 `bytesOutstanding < idealSendBuffer` 时才发送数据
- 计算可发送的数据量：`availableBuffer = idealSendBuffer - bytesOutstanding`
- 更新流控计数器：`bytesOutstanding` 和 `bytesSent`
- 发送失败时回滚计数器

### 2. StreamCallback 事件处理

#### QUIC_STREAM_EVENT_IDEAL_SEND_BUFFER_SIZE

```cpp
case QUIC_STREAM_EVENT_IDEAL_SEND_BUFFER_SIZE:
    if (!socketContext->useDirectSendMode &&
        socketContext->idealSendBuffer != Event->IDEAL_SEND_BUFFER_SIZE.ByteCount) {
        
        socketContext->idealSendBuffer = Event->IDEAL_SEND_BUFFER_SIZE.ByteCount;
        
        // If we have pending data and room in the buffer, try to send more
        if (socketContext->sendList != NULL && 
            socketContext->bytesOutstanding < socketContext->idealSendBuffer) {
            SendDataFromSocketBufferList(socketContext);
        }
    }
```

**功能**：
- 更新理想发送缓冲区大小
- 如果有待发送数据且有缓冲区空间，继续发送

#### QUIC_STREAM_EVENT_SEND_COMPLETE

```cpp
case QUIC_STREAM_EVENT_SEND_COMPLETE:
    // Update flow control counters
    if (sentLength > 0) {
        socketContext->bytesOutstanding -= sentLength;
        socketContext->bytesAcked += sentLength;
    }
    
    // Remove sent buffers from send list
    // Continue sending if more data available and buffer has room
    if (hasMoreData && socketContext->bytesOutstanding < socketContext->idealSendBuffer) {
        SendDataFromSocketBufferList(socketContext);
    }
```

**功能**：
- 更新流控计数器
- 从发送列表中移除已发送的缓冲区
- 如果还有数据且缓冲区有空间，继续发送

### 3. QuicWrite/QuicWritev 更新

在非 `useDirectSendMode` 模式下：

```cpp
// Check if we need to start a send operation with flow control
BOOLEAN needToSend = !context->sendQuicBuffer[0].Buffer &&
                   context->bytesOutstanding < context->idealSendBuffer;

// If no send is in progress and we have room, start one
if (needToSend) {
    SendDataFromSocketBufferList(context);
}
```

**功能**：
- 将数据复制到缓冲区列表
- 检查流控状态决定是否立即发送
- 避免发送过多数据导致内存压力

## 流控逻辑流程

### 发送流程

1. **应用调用 QuicWrite/QuicWritev**
2. **数据复制到缓冲区列表**
3. **检查流控条件**：`bytesOutstanding < idealSendBuffer`
4. **如果有空间，调用 SendDataFromSocketBufferList()**
5. **更新 bytesOutstanding 和 bytesSent**
6. **调用 MsQuic->StreamSend()**

### 确认流程

1. **收到 QUIC_STREAM_EVENT_SEND_COMPLETE 事件**
2. **更新流控计数器**：
   - `bytesOutstanding -= sentLength`
   - `bytesAcked += sentLength`
3. **从发送列表移除已发送的缓冲区**
4. **检查是否还有数据要发送**
5. **如果有数据且有缓冲区空间，继续发送**

### 缓冲区大小调整

1. **收到 QUIC_STREAM_EVENT_IDEAL_SEND_BUFFER_SIZE 事件**
2. **更新 idealSendBuffer**
3. **如果新的缓冲区大小更大且有待发送数据，立即尝试发送**

## 优势

1. **内存效率**：避免发送过多数据导致内存压力
2. **网络效率**：根据网络状况动态调整发送量
3. **吞吐量优化**：保持管道满载但不过载
4. **兼容性**：与现有的 useDirectSendMode 模式兼容
5. **简化实现**：专注于单stream场景，实现更简洁

## 使用方式

```cpp
// 创建socket
SocketOptions options = {0};
options.mode = MODE_CLIENT;
options.type = TYPE_QUIC;
P2P_SOCKET socket = P2pCreate(&options);

// 连接到服务器
P2pConnect(socket, "127.0.0.1", 12345);

// 设置为非direct send mode以启用流控
P2pSetSendMode(socket, 0);

// 正常写入数据，流控会自动管理
char buffer[1024];
int sent = P2pWrite(socket, buffer, sizeof(buffer));
```

流控机制会自动：
- 管理发送缓冲区
- 根据网络状况调整发送速率
- 确保不会发送过多数据导致内存问题
- 在网络允许时最大化吞吐量

## 测试

可以使用提供的 `test_single_stream_flow_control.cpp` 测试程序来验证流控机制的正确性。

## 与多stream方案的区别

- **作用域**：单stream方案的流控变量在 `P2P_SOCKET_CONTEXT` 中，多stream方案在 `P2P_STREAM_CONTEXT` 中
- **API**：单stream使用 QuicWrite/QuicWritev，多stream使用 P2pStreamWrite/P2pStreamWritev
- **复杂度**：单stream实现更简单，适合现有代码的渐进式升级
- **兼容性**：完全向后兼容现有的单stream使用方式
