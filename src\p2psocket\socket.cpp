// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#include "socket.h"
#include "common.h"
int SocketInterface ::P2pBind2(const char* ipaddr, int port[], int size) {
  return E_UNSUPPORT;
}

int SocketInterface ::P2pRead(char* buffer, int len, int timeout) {
  return E_UNSUPPORT;
}

SocketInterface* SocketInterface ::P2pAccept() {
  return P2pAccept(nullptr, 0, nullptr);
}

int SocketInterface ::P2pWritev(struct p2p_iovec* iov, int count) {
  int totallen = 0;
  if (iov == nullptr || count <= 0)
    return -1;
  for (int i = 0; i < count; i++) {
    totallen += iov[i].iov_len;
  }
  while (count) {
    int sent = 0;
    int sizeToSend = static_cast<int>(iov->iov_len);
    for (int length = 0; length < sizeToSend; length += sent) {
      // Send a chunk of data
      sent = P2pWrite(static_cast<const char*>(iov->iov_base) + length,
                      sizeToSend - length);
      totallen += sent;
      // Check for errors
      if (sent < 0)
        return totallen;
    }
    iov++;
    count--;
  }
  return totallen;
}
int SocketInterface ::P2pReadv(struct p2p_iovec* iov, int count) {
  return E_UNSUPPORT;
}
int SocketInterface ::P2pReadv(struct p2p_iovec* iov, int count, int timeout) {
  return E_UNSUPPORT;
}
int SocketInterface ::P2pSetNonBlocking() {
  return E_UNSUPPORT;
}

int SocketInterface ::P2pSetConnTimeout(int timeout) {
  return E_UNSUPPORT;
}

int SocketInterface ::P2pShutdown() {
  return E_UNSUPPORT;
}
int SocketInterface ::P2pRegisterStateChanged(
    std::function<void(int, int)> callback) {
  return E_UNSUPPORT;
}

int SocketInterface ::P2pGetLocalPorts(int* ports, int* size) {
  return E_UNSUPPORT;
}
int SocketInterface ::P2pSetRecvTimeout(int timeout) {
  return E_UNSUPPORT;
}
int SocketInterface ::P2pSetSendTimeout(int timeout) {
  return E_UNSUPPORT;
}
int SocketInterface ::P2pPoll(PollEvent* events, int timeout) {
  return E_UNSUPPORT;
}
int SocketInterface ::P2pSetCertVerifyCallback(
    std::function<bool(std::string& str)> callback) {
  return E_UNSUPPORT;
}
void SocketInterface ::SetSocketIdx(int idx) {
  socket_idx_ = idx;
}
int SocketInterface ::GetSocketIdx() {
  return socket_idx_;
}
void SocketInterface ::SetRemoteIp(char* remote_ip) {
  remote_ip_ = remote_ip;
}
const char* SocketInterface ::GetRemoteIp() {
  return remote_ip_.c_str();
}
void SocketInterface ::SetRemotePort(int remote_port) {
  remote_port_ = remote_port;
}
int SocketInterface ::GetRemotePort() {
  return remote_port_;
}

int SocketInterface::P2pSetSendMode(int directMode) {
    return 0;
}

int SocketInterface::P2pSetReadMode(int directMode) {
    return 0;
}

// Stream API default implementations (return E_UNSUPPORT for non-QUIC sockets)
P2P_STREAM SocketInterface::P2pStreamCreate(struct StreamOptions* options) {
    return nullptr; // Not supported by default
}

int SocketInterface::P2pStreamClose(P2P_STREAM stream) {
    return E_UNSUPPORT;
}

int SocketInterface::P2pStreamWrite(P2P_STREAM stream, const char* buffer, int len) {
    return E_UNSUPPORT;
}

int SocketInterface::P2pStreamWritev(P2P_STREAM stream, struct p2p_iovec* iov, int count) {
    return E_UNSUPPORT;
}

int SocketInterface::P2pStreamRead(P2P_STREAM stream, char* buffer, int len) {
    return E_UNSUPPORT;
}

int SocketInterface::P2pStreamPoll(P2P_STREAM stream, struct StreamEvent* events, int max_events, int timeout) {
    return E_UNSUPPORT;
}

int SocketInterface::P2pStreamGetState(P2P_STREAM stream) {
    return E_UNSUPPORT;
}

int SocketInterface::P2pStreamSetCallback(P2P_STREAM stream, StreamEventCallback callback, void* user_data) {
    return E_UNSUPPORT;
}

int SocketInterface::P2pStreamGetId(P2P_STREAM stream) {
    return E_UNSUPPORT;
}

int SocketInterface::P2pStreamGetBufferedBytes(P2P_STREAM stream) {
    return E_UNSUPPORT;
}

P2P_SOCKET SocketInterface::P2pStreamGetSocket(P2P_STREAM stream) {
    return nullptr;
}
